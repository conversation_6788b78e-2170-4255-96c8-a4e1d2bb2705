import { <PERSON>ContextID, PlayerContextID } from "./contextIds";
import { GameContextStatus, GameFlowContext } from "./context/gamecontext";
import { ContextManager } from "./contextmanager/contextManager";
import { getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { EventIdValue, WalletOperation } from "./wallet";
import { EventHistory, GameHistoryService, GetGameContextsRequest, RoundHistory } from "./playerGameHistory";
import { PlayerContext } from "./playercontext/playerContext";
import { publicId } from "@skywind-group/sw-utils";
import { GameMode } from "@skywind-group/sw-game-core";
import { PlayMode } from "./playMode";

export interface UnfinishedRoundHistory extends RoundHistory {
    status: GameContextStatus;
    pendingSpin?: EventHistory;
    gameContextId: string;
}

export interface UnfinishedHistoryRequest {
    brandId: number;
    playerCode?: string;
    gameCode?: string;
    ts__lt?: number;
    ts__lte?: number;
    ts__gt?: number;
    ts__gte?: number;
    status?: GameContextStatus;
    roundId?: number;
    sortOrder?: "ASC" | "DESC";
    includeBrokenSpin?: boolean;
    gameContextId?: string;
}

export function getUnfinishedGameHistoryService(): UnfinishedGameHistoryService {
    return new UnfinishedGameHistoryServiceImpl(getGameFlowContextManager());
}

export interface UnfinishedGameHistoryService {
    getPlayerUnfinishedHistory(request: UnfinishedHistoryRequest): Promise<UnfinishedRoundHistory[]>;

    getGameContexts(request: GetGameContextsRequest): Promise<GameFlowContext[]>;
}

export class UnfinishedGameHistoryServiceImpl implements UnfinishedGameHistoryService {

    private readonly unfinishedFilter = (req: UnfinishedHistoryRequest, i: UnfinishedRoundHistory) => i !== undefined &&
        (!req.ts__gte || i.firstTs.getTime() >= req.ts__gte) &&
        (!req.ts__lte || i.firstTs.getTime() <= req.ts__lte) &&
        (!req.ts__gt || i.firstTs.getTime() > req.ts__gt) &&
        (!req.ts__lt || i.firstTs.getTime() < req.ts__lt) &&
        // tslint:disable-next-line:triple-equals
        (req.roundId == undefined || i.roundId == req.roundId) && // checking for undefined because of roundId=0
        (!req.gameContextId || i.gameContextId === req.gameContextId) &&
        (!req.status || i.status === req.status)

    private readonly unfinishedSort = (a: UnfinishedRoundHistory,
        b: UnfinishedRoundHistory) => a.firstTs.getTime() - b.firstTs.getTime()

    constructor(private readonly manager: ContextManager) {
    }

    public async getPlayerUnfinishedHistory(request: UnfinishedHistoryRequest): Promise<UnfinishedRoundHistory[]> {
        let gameContextIDs: GameContextID[];
        if (request.playerCode) {
            const playerContextID = PlayerContextID.create(request.brandId, request.playerCode);
            let playerContext = await this.manager.findPlayerContextById(playerContextID);
            if (!playerContext) {
                playerContext = await this.manager.findOfflinePlayerContext(playerContextID);
            }
            gameContextIDs = this.getGameContextIds(request, playerContext);
        } else if (request.gameContextId) {
            gameContextIDs = [GameContextID.createFromString(request.gameContextId)];
        } else if (request.roundId) {
            const roundId = +request.roundId ? +request.roundId : publicId.instance.decode(request.roundId);
            const dbGameContext = await this.manager.findGameContextByRoundId(roundId);
            if (dbGameContext?.brandId === request.brandId) {
                gameContextIDs = [GameContextID.createFromString(dbGameContext.id)];
            }
        } else {
            gameContextIDs = await this.manager.findTopActiveGameContextIDsByBrand(
                request.brandId,
                Number.MAX_SAFE_INTEGER
            );
        }
        if (gameContextIDs?.length === 0) {
            return [];
        }

        const gameFlowContexts: GameFlowContext[] = await Promise.all(
            gameContextIDs.map(id => this.manager.findGameContextWithoutRestoring(id)));
        if (gameFlowContexts.length === 0) {
            return [];
        }

        const result = gameFlowContexts.map((context) => {
            if (context && context.broken) {
                const unfinishedItem: UnfinishedRoundHistory = {
                    ...GameHistoryService.asRoundHistoryInfo(context),
                    status: context.status,
                    gameContextId: context.id.asString()
                };
                if (request.includeBrokenSpin && unfinishedItem.status === "broken") {
                    this.addPendingSpinData(unfinishedItem, context);
                }
                return unfinishedItem;
            }
        }).filter(item => this.unfinishedFilter(request, item));

        if (request.sortOrder === "ASC") {
            return result.sort(this.unfinishedSort);
        } else if (request.sortOrder === "DESC") {
            return result.sort((a, b) => this.unfinishedSort(b, a));
        }
        return result;
    }

    /**
     * Check playerContext for existing gameContextIds. If such present - its whether an active whether a broken game.
     * If playerContext is missing (it was not found nor in redis nor in Postrgres) - then, if gameCode was specified
     * in request object, it will return gameContextIds for this gameCode.
     */
    private getGameContextIds(request: UnfinishedHistoryRequest, playerContext?: PlayerContext): GameContextID[] {
        if (playerContext) {
            let gameContextIDs = playerContext.activeGames.map(g => g.id)
                .concat(playerContext.brokenGames.map(g => g.id));

            if (request.gameCode) {
                gameContextIDs = gameContextIDs.filter(g => g.gameCode === request.gameCode);
            }

            return gameContextIDs;
        }
        if (request.gameCode) {
            return [this.buildGameContextId(request, "mobile"), this.buildGameContextId(request, "web")];
        }
        return [];
    }

    private buildGameContextId(request: UnfinishedHistoryRequest, deviceId: "mobile" | "web"): GameContextID {
        if (request.gameContextId) {
            return GameContextID.createFromString(request.gameContextId);
        }

        return GameContextID.create(request.gameCode, request.brandId, request.playerCode, deviceId);
    }

    public async getGameContexts(request: GetGameContextsRequest): Promise<GameFlowContext[]> {
        const result: GameFlowContext[] = [];
        for (const playMode of PlayMode.GAME_MODES) {
            const manager = getGameFlowContextManager(playMode);
            for (const deviceId of ["web", "mobile"]) {
                const id = GameContextID.create(request.gameCode, request.brandId, request.playerCode, deviceId, playMode);
                const context = await manager.findGameContextWithoutRestoring(id);
                if (context) {
                    result.push(context);
                }
            }
        }
        return result;
    }

    private addPendingSpinData(unfinishedItem: UnfinishedRoundHistory, context: GameFlowContext): void {
        const walletOperation: WalletOperation & EventIdValue = context.pendingModification ?
            context.pendingModification.walletOperation :
            context.jackpotPending.walletOperation;

        const history = context.pendingModification ?
            context.pendingModification.history :
            context.jackpotPending.history;

        const spin: EventHistory = {
            spinNumber: walletOperation.eventId || 0,
            type: history ? history.type : undefined,
            endOfRound: history ? history.roundEnded : undefined,
            walletTransactionId: walletOperation.transactionId,
            win: walletOperation?.win || 0,
            bet: walletOperation?.bet || 0,
            balanceBefore: undefined,
            balanceAfter: undefined,
            ts: undefined,
            test: context.gameData.gameTokenData.test
        };
        unfinishedItem.pendingSpin = spin;
    }
}
