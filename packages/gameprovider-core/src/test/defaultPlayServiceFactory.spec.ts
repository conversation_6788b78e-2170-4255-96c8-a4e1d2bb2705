import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { DefaultPlayServiceFactory } from "../skywind/playservicefactory/defaultPlayServiceFactory";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { OperatorInfo } from "@skywind-group/sw-management-gameprovider";
import * as sinon from "sinon";

@suite()
export class DefaultPlayServiceFactorySpec {

    private factory: DefaultPlayServiceFactory;
    private mockOperatorInfo: OperatorInfo;

    public before() {
        this.factory = new DefaultPlayServiceFactory();
        
        // Setup mock operator info
        this.mockOperatorInfo = {
            id: 1,
            typeInfo: {
                type: "test",
                version: "1.0"
            },
            info: {
                code: "TEST",
                params: {}
            }
        } as OperatorInfo;
    }

    public after() {
        sinon.restore();
    }

    @test()
    public async createPlayServiceForRealMode() {
        const playService = await this.factory.create(this.mockOperatorInfo, PlayMode.REAL);
        
        expect(playService).to.not.be.undefined;
        expect(playService).to.not.be.null;
    }

    @test()
    public async createPlayServiceForPlayMoneyMode() {
        const playService = await this.factory.create(this.mockOperatorInfo, PlayMode.PLAY_MONEY);
        
        expect(playService).to.not.be.undefined;
        expect(playService).to.not.be.null;
    }

    @test()
    public async createPlayServiceForBNSMode() {
        const playService = await this.factory.create(this.mockOperatorInfo, PlayMode.BNS);
        
        expect(playService).to.not.be.undefined;
        expect(playService).to.not.be.null;
    }

    @test()
    public async createPlayServiceForFunBonusMode() {
        // FUN_BONUS mode should create a play service successfully
        // It should use the standard MerchantPlayService (like REAL mode)
        const playService = await this.factory.create(this.mockOperatorInfo, PlayMode.FUN_BONUS);
        
        expect(playService).to.not.be.undefined;
        expect(playService).to.not.be.null;
    }

    @test()
    public async createPlayServiceForFunModeThrowsError() {
        // FUN mode should be rejected
        try {
            await this.factory.create(this.mockOperatorInfo, PlayMode.FUN);
            expect.fail("Should have thrown an error for FUN mode");
        } catch (error) {
            expect(error.message).to.include("Request is not supported in fun mode");
        }
    }

    @test()
    public async createPlayServiceWithTransferEnabled() {
        const playService = await this.factory.create(this.mockOperatorInfo, PlayMode.FUN_BONUS, true);
        
        expect(playService).to.not.be.undefined;
        expect(playService).to.not.be.null;
    }

    @test()
    public async createPlayServiceForMerchantWithFunBonus() {
        // Setup merchant operator info
        const merchantOperatorInfo = {
            ...this.mockOperatorInfo,
            info: {
                ...this.mockOperatorInfo.info,
                params: {
                    walletPerGame: false
                }
            }
        };

        const playService = await this.factory.create(merchantOperatorInfo, PlayMode.FUN_BONUS);
        
        expect(playService).to.not.be.undefined;
        expect(playService).to.not.be.null;
    }
}
