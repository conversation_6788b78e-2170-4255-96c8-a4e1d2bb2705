import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { validateFunBonus, isCurrencyFunBonusEnabled, getFunBonusValidationError } from "../../skywind/utils/validateFunBonus";
import { BrandEntity } from "../../skywind/entities/brand";
import { Merchant } from "../../skywind/entities/merchant";
import * as Errors from "../../skywind/errors";
import * as sinon from "sinon";

@suite()
export class ValidateFunBonusSpec {

    private currenciesStub: sinon.SinonStub;
    private mockBrand: BrandEntity;
    private mockMerchant: Merchant;

    public before() {
        // Mock the Currencies module
        this.currenciesStub = sinon.stub();
        const mockCurrencies = {
            value: this.currenciesStub
        };
        
        // Replace the import
        const currencyExchange = require("@skywind-group/sw-currency-exchange");
        sinon.stub(currencyExchange, "Currencies").value(mockCurrencies);

        // Setup mock entities
        this.mockBrand = { id: 1, name: "Test Brand" } as BrandEntity;
        this.mockMerchant = { id: 1, code: "TEST" } as Merchant;
    }

    public after() {
        sinon.restore();
    }

    public beforeEach() {
        this.currenciesStub.reset();
    }

    @test()
    public validateFunBonusSucceedsForValidFunBonusCurrency() {
        // Setup mock currency with funBonus property
        this.currenciesStub.withArgs("FUNUSD").returns({ funBonus: true });

        const result = validateFunBonus(this.mockBrand, "FUNUSD", this.mockMerchant);
        
        expect(result).to.be.true;
    }

    @test()
    public validateFunBonusThrowsErrorForCurrencyWithoutFunBonusProperty() {
        // Setup mock currency without funBonus property
        this.currenciesStub.withArgs("USD").returns({ funBonus: false });

        expect(() => {
            validateFunBonus(this.mockBrand, "USD", this.mockMerchant);
        }).to.throw(Errors.CurrencyNotFunBonusEnabledError);
    }

    @test()
    public validateFunBonusThrowsErrorForUnknownCurrency() {
        // Setup mock to throw error for unknown currency
        this.currenciesStub.withArgs("UNKNOWN").throws(new Error("Currency not found"));

        expect(() => {
            validateFunBonus(this.mockBrand, "UNKNOWN", this.mockMerchant);
        }).to.throw(Errors.CurrencyNotFunBonusEnabledError);
    }

    @test()
    public validateFunBonusThrowsErrorForNullCurrency() {
        // Setup mock to return null
        this.currenciesStub.withArgs("NULL").returns(null);

        expect(() => {
            validateFunBonus(this.mockBrand, "NULL", this.mockMerchant);
        }).to.throw(Errors.FunBonusCurrencyNotFoundError);
    }

    @test()
    public isCurrencyFunBonusEnabledReturnsTrueForFunBonusCurrency() {
        this.currenciesStub.withArgs("FUNUSD").returns({ funBonus: true });

        const result = isCurrencyFunBonusEnabled("FUNUSD");
        
        expect(result).to.be.true;
    }

    @test()
    public isCurrencyFunBonusEnabledReturnsFalseForNonFunBonusCurrency() {
        this.currenciesStub.withArgs("USD").returns({ funBonus: false });

        const result = isCurrencyFunBonusEnabled("USD");
        
        expect(result).to.be.false;
    }

    @test()
    public isCurrencyFunBonusEnabledReturnsFalseForUnknownCurrency() {
        this.currenciesStub.withArgs("UNKNOWN").throws(new Error("Currency not found"));

        const result = isCurrencyFunBonusEnabled("UNKNOWN");
        
        expect(result).to.be.false;
    }

    @test()
    public getFunBonusValidationErrorReturnsCorrectMessageForValidCurrency() {
        this.currenciesStub.withArgs("FUNUSD").returns({ funBonus: true });

        const result = getFunBonusValidationError("FUNUSD");
        
        expect(result).to.equal("Currency FUNUSD validation passed.");
    }

    @test()
    public getFunBonusValidationErrorReturnsCorrectMessageForNonFunBonusCurrency() {
        this.currenciesStub.withArgs("USD").returns({ funBonus: false });

        const result = getFunBonusValidationError("USD");
        
        expect(result).to.equal("Currency USD is not enabled for fun bonus mode. Only currencies with funBonus property can be used in fun bonus mode.");
    }

    @test()
    public getFunBonusValidationErrorReturnsCorrectMessageForUnknownCurrency() {
        this.currenciesStub.withArgs("UNKNOWN").throws(new Error("Currency not found"));

        const result = getFunBonusValidationError("UNKNOWN");
        
        expect(result).to.equal("Currency UNKNOWN not found or not configured for fun bonus mode.");
    }

    @test()
    public getFunBonusValidationErrorReturnsCorrectMessageForNullCurrency() {
        this.currenciesStub.withArgs("NULL").returns(null);

        const result = getFunBonusValidationError("NULL");
        
        expect(result).to.equal("Currency NULL not found or not configured for fun bonus mode.");
    }
}
